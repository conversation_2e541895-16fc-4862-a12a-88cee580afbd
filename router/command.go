package router

import (
	"fwyytool/cmd"
	"fwyytool/helpers"
	"fwyytool/libs/utils"
	"fwyytool/service/monitor"
	"fwyytool/service/tools"
	"git.zuoyebang.cc/pkg/golib/v2/command"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/spf13/cobra"
)

/*
		命令行使用方式(可通过执行 go run main.go -h  查看)：
		goweb application

		Usage:
	  		goweb [command]

		Available Commands:
	  		job1    This is a job to do xxx
	  		job2    This is a job to do yyy

Flags:

	  -h, --help   help for goweb


		为了方便，go run main.go 默认启动http服务。
		go run main.go command  启动一个任务，比如，go run main.go job1
*/
func Commands(rootCmd *cobra.Command, engine *gin.Engine) {
	// 初始化cron任务所需资源
	helpers.InitResourceForCron(engine)
	// command 相关在 cmd 目录
	cmd.RegisterCommand(engine, rootCmd)

	if utils.GetEnvName(nil) == "tips" {
		cycleJob := command.InitCycle(engine)
		cycleJob.AddFunc(time.Hour*1, func(ctx *gin.Context) error {
			if time.Now().Hour() == 9 {
				_ = monitor.TeacherService.Today(ctx)
			}
			return nil
		})
		cycleJob.AddFunc(time.Second*10, func(ctx *gin.Context) error {
			_ = tools.HttpTestService.AnalysisTrace(ctx)
			return nil
		})
		cycleJob.Start()
	}

	// 定时任务
	//AddDataDiffNoticeScript(engine)
}

func AddDataDiffNoticeScript(engine *gin.Engine) {
	// 只在 tips 跑
	if utils.GetEnvName(nil) != "tips" {
		return
	}
	cronJob := command.InitCrontab(engine)
	// 每天早上 11 点通知
	cronJob.AddFunc("0 0 11 * * *", func(context *gin.Context) error {
		err := monitor.DataDiffService.YesterdayReport(context)
		zlog.Infof(context, "AddDataDiffNoticeScript task start")
		if err != nil {
			zlog.Errorf(context, "AddDataDiffNoticeScript err,error:%v", err)
			return err
		}
		return nil
	})
	cronJob.Start()
}
