officeserver:
  # 调用下游的服务名称
  service: officeserver
  # 请求完整地址
  # domain: http://office-server-svc.inf:8080
  domain: http://office-server-base-cc.suanshubang.cc
  # 超时配置，time.Duration 类型
  timeout: 30s
  # 到该服务的最大空闲连接数
  maxIdleConns: 100
  # 空闲连接超时关闭时间
  idleConnTimeout: 300s
  # 重试次数，最多执行retry+1次
  retry: 0

tower:
  service: tower
  domain: http://tower-svc.support:8080
  timeout: 1000ms
  httpStat: true
  retry: 1

jxnotice:
  service: jxnotice
  # 请求完整地址
  domain: http://jxnotice-svc.edu:8080
  timeout: 1500ms
  retry: 1

livestation:
  service: livestation
  # 请求完整地址
  domain: http://livestation-svc.edu:8080
  timeout: 1000ms
  retry: 1

userprofile:
  service: userprofile
  domain: http://userprofile-svc.support:8080
  timeout: 1500ms
  retry: 1

assistantdesk:
  service: assistantdesk
  domain: http://arkgo-svc.support-arktest:8080
  timeout: 5000ms
  retry: 1


assistantdeskgo:
  service: assistantdesk
  domain: http://assistantdesk-arktest-cc.suanshubang.cc
  timeout: 1000ms
  retry: 1

arkgo:
  service: arkgo
  domain: http://assistantdesk-base-cc.suanshubang.cc
  timeout: 15000ms
  retry: 1

genke:
  service: genke
  domain: http://genke-svc.support-fudaoyz:8080
  timeout: 1000ms
  retry: 1

moat:
  service: moat
  domain: http://moat2-svc.sell-gateway:8080
  timeout: 5000ms
  retry: 1

dataproxy:
  service: dataproxy
  domain: http://dataproxy-svc.support-fudaoyz:8080
  timeout: 1000ms
  retry: 1

zbcoredal:
  service: zbcoredal
  domain: http://dal-svc.sell-course:8080
  timeout: 1500ms
  httpStat: true
  retry: 1

zbcoredat:
  service: zbcoredat
  domain: http://dat-svc.sell-course:8080
  timeout: 1500ms
  httpStat: true
  retry: 1

zbcoredau:
  service: zbcoredau
  domain: http://dau-svc.sell-course:8080
  timeout: 1500ms
  httpStat: true
  retry: 1

zbcoredas:
  service: zbcoredas
  domain: http://das-svc.sell-course:8080
  timeout: 1500ms
  httpStat: true
  retry: 1

zbcoredar:
  service: zbcoredar
  domain: http://moat2-svc.sell-gateway:8080
  timeout: 1500ms
  httpStat: true
  retry: 1
  moatAppKey: assistantdesk
  moatAppSecret: 86ec2d3211ac6e7ff5fcbd95fbf4e2c0

longservice:
  service: longservice
  domain: http://172.29.240.97:8090
  timeout: 1000ms
  httpStat: true
  retry: 1
assistantcourse:
  service: assistantcourse
  domain: http://assistantdesk-base-cc.suanshubang.cc
  timeout: 1000ms
  httpStat: true
  retry: 1

coursebase:
  service: coursebase
  domain: http://coursebase-svc.sell-course:8080
  timeout: 3000ms
  httpStat: true
  retry: 1

coursesearch:
  service: coursesearch
  domain: http://sellmis-base-cc.suanshubang.cc
  timeout: 1500ms
  retry: 1

mesh:
  service: mesh
  domain: http://fwyy-mesh-svc.support:8080
  timeout: 1500ms
  httpStat: true
  retry: 1

kunpeng:
  service: kunpeng
  domain: http://kp.zuoyebang.cc
  timeout: 2000ms
  retry: 3

kpstaff:
  service: kpstaff
  domain: http://kpstaff-svc.kunpeng:8080
  protocol: http
  timeout: 2000ms
  retry: 3

intratraffic:
  service: intratraffic
  domain: https://intra-traffic.zuoyebang.cc
  protocol: http
  timeout: 2000ms
  retry: 3

mkgray:
  # 调用下游的服务名称
  service: mkgray
  # 请求完整地址
  #  domain: http://mkgray-svc.mark:8080
  domain: http://zhkt-base-cc.suanshubang.cc
  # 超时配置，time.Duration 类型
  timeout: 3s
  # 重试次数，最多执行retry+1次
  retry: 0

touchmis:
  service: touchmis
  domain: http://touchmis-svc.lpc:8080
  timeout: 3000ms
  retry: 1

touchmisgo:
  service: touchmisgo
  domain: http://touchmisgo-svc.support:8080
  timeout: 3000ms
  retry: 1

chain:
  service: chain
  domain: https://chain-base-cc.suanshubang.cc
  timeout: 6000ms
  retry: 1

achilles:
  service: achilles
  domain: http://achilles-v3-server-svc.edu:8080
  timeout: 15000ms
  retry: 1

moat2:
  service: moat2
  domain: http://moat2-svc.sell-gateway:8080
  timeout: 15000ms
  retry: 1

ares:
  service: ares
  domain: http://ares-svc.sell-course:8080
  timeout: 15000ms
  retry: 1

artcw:
  service: artcw
  domain: http://artcw-svc.edu-research:8080
  timeout: 15000ms
  retry: 1

assignclass:
  service: assignclass
  domain: http://bzr-assignclass-svc.support:8080
  timeout: 15000ms
  retry: 1

assistantai:
  service: assistant-ai
  domain: http://assistant-ai-svc.support:8080
  timeout: 15000ms
  retry: 1

assistantcoursego:
  service: assistantcoursego
  domain: http://assistantcoursego-svc.support:8080
  timeout: 15000ms
  retry: 1

assistantdataware:
  service: assistantdataware
  domain: http://assistantdataware-svc.support:8080
  timeout: 15000ms
  retry: 1

assistantweb:
  service: assistantweb
  domain: http://assistantweb-svc.support:8080
  timeout: 15000ms
  retry: 1

assistantwx:
  service: assistantwx
  domain: http://assistantwx-svc.support:8080
  timeout: 15000ms
  retry: 1

backend:
  service: backend
  domain: http://backend-svc.xuexiguanjia:8080
  timeout: 15000ms
  retry: 1

report:
  service: report
  domain: http://report-svc.deer:8080
  timeout: 15000ms
  retry: 1

stallman:
  service: stallman
  domain: http://stallman-svc.deer:8080
  timeout: 15000ms
  retry: 1

