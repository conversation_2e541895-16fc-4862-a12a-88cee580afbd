package tools

import (
	"fwyytool/api/userprofile"
	"fwyytool/components"
	"fwyytool/consts"
	toolsInput "fwyytool/controllers/http/tools/input"
	"fwyytool/service/tools"

	"sync"
	"time"

	"git.zuoyebang.cc/pkg/golib/v2/base"
	"github.com/gin-gonic/gin"
	"github.com/pkg/errors"
)

var HttpTestController httpTestController

type httpTestController struct {
}

// 频次限制相关
var (
	// 用户最后调用时间记录，key为用户ID，value为最后调用时间
	userLastCallTime = make(map[int64]time.Time)
	// 保护并发访问的互斥锁
	callTimeMutex = sync.RWMutex{}
	// 调用间隔限制（3秒）
	callInterval = time.Second * 3
)

// checkRateLimit 检查用户调用频次限制
func (s httpTestController) checkRateLimit(ctx *gin.Context) error {
	// 获取用户信息
	user, exists := ctx.Get(consts.LOGIN_USER_INFO)
	if !exists {
		return errors.New("用户未登录")
	}

	userInfo, ok := user.(*userprofile.UserInfo)
	if !ok {
		return errors.New("用户信息格式错误")
	}

	userID := int64(userInfo.UserId)
	now := time.Now()

	callTimeMutex.Lock()
	defer callTimeMutex.Unlock()

	// 检查用户上次调用时间
	if lastCallTime, exists := userLastCallTime[userID]; exists {
		// 计算距离上次调用的时间间隔
		timeSinceLastCall := now.Sub(lastCallTime)
		if timeSinceLastCall < callInterval {
			// 还在限制时间内，返回错误
			remainingTime := callInterval - timeSinceLastCall
			return errors.Errorf("调用过于频繁，请等待 %.1f 秒后再试", remainingTime.Seconds())
		}
	}

	// 更新用户最后调用时间
	userLastCallTime[userID] = now

	// 清理过期的记录（超过1小时的记录）
	cutoffTime := now.Add(-time.Hour)
	for uid, lastTime := range userLastCallTime {
		if lastTime.Before(cutoffTime) {
			delete(userLastCallTime, uid)
		}
	}

	return nil
}

func (s httpTestController) Call(ctx *gin.Context) {
	// 检查频次限制
	if err := s.checkRateLimit(ctx); err != nil {
		base.RenderJsonFail(ctx, components.SystemErr(err.Error()))
		return
	}

	var params toolsInput.HttpTestCallParam
	if err := ctx.ShouldBind(&params); err != nil {
		base.RenderJsonFail(ctx, errors.Wrap(components.ErrorParamInvalid, err.Error()))
		return
	}

	err := params.Validate(ctx)
	if err != nil {
		base.RenderJsonFail(ctx, errors.Wrap(components.ErrorParamInvalid, err.Error()))
		return
	}

	resp, err := tools.HttpTestService.Call(ctx, params)
	if err != nil {
		base.RenderJsonFail(ctx, components.SystemErr(err.Error()))
		return
	}

	base.RenderJsonSucc(ctx, resp)
	return
}

func (s httpTestController) GetReportList(ctx *gin.Context) {
	var params toolsInput.GetReportListParam
	if err := ctx.ShouldBind(&params); err != nil {
		base.RenderJsonFail(ctx, errors.Wrap(components.ErrorParamInvalid, err.Error()))
		return
	}

	err := params.Validate(ctx)
	if err != nil {
		base.RenderJsonFail(ctx, errors.Wrap(components.ErrorParamInvalid, err.Error()))
		return
	}

	resp, err := tools.HttpTestService.GetReportList(ctx, params)
	if err != nil {
		base.RenderJsonFail(ctx, components.SystemErr(err.Error()))
		return
	}

	base.RenderJsonSucc(ctx, resp)
	return
}

func (s httpTestController) Router(ctx *gin.Context) {
	var params toolsInput.HttpTestRouterParam

	// 从查询字符串获取domain参数
	if err := ctx.ShouldBindQuery(&params); err != nil {
		base.RenderJsonFail(ctx, errors.Wrap(components.ErrorParamInvalid, err.Error()))
		return
	}

	err := params.Validate(ctx)
	if err != nil {
		base.RenderJsonFail(ctx, errors.Wrap(components.ErrorParamInvalid, err.Error()))
		return
	}

	err = tools.HttpTestService.Router(ctx, params)
	if err != nil {
		base.RenderJsonFail(ctx, components.SystemErr(err.Error()))
		return
	}
}

func (s httpTestController) ClearQWUrlCache(ctx *gin.Context) {
	var params toolsInput.HttpTestClearQWUrlCacheParam
	if err := ctx.ShouldBind(&params); err != nil {
		base.RenderJsonFail(ctx, errors.Wrap(components.ErrorParamInvalid, err.Error()))
		return
	}

	err := tools.ClearQWCache(ctx, params.MinID, params.MaxID)
	if err != nil {
		base.RenderJsonFail(ctx, components.SystemErr(err.Error()))
		return
	}

	base.RenderJsonSucc(ctx, "success")
	return
}
